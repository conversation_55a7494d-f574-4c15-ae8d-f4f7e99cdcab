<template>
  <div class="vue-office-preview">
    <!-- 加载状态 -->
    <div v-if="loading" class="preview-loading">
      <el-icon class="is-loading" :size="48">
        <Loading />
      </el-icon>
      <p>正在加载预览...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="preview-error">
      <el-icon :size="48" color="#F56C6C">
        <Warning />
      </el-icon>
      <p>{{ error }}</p>
    </div>
    
    <!-- Vue Office 预览组件 -->
    <div v-else class="office-preview-container">
      <!-- Word 文档预览 -->
      <vue-office-docx
        v-if="previewType === 'docx'"
        :src="documentSrc"
        :style="previewStyle"
        @rendered="handleRendered"
        @error="handleError"
      />
      
      <!-- Excel 文档预览 -->
      <vue-office-excel
        v-else-if="previewType === 'excel'"
        :src="documentSrc"
        :style="previewStyle"
        @rendered="handleRendered"
        @error="handleError"
      />
      
      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <el-icon :size="64" color="#909399">
          <Document />
        </el-icon>
        <h3>暂不支持预览此文件类型</h3>
        <p>支持的格式：Word (.docx, .doc)、Excel (.xlsx, .xls)</p>
        <p>PowerPoint (.pptx, .ppt) 和 PDF (.pdf) 请使用专门的预览器</p>
        <p class="file-info">当前文件：{{ props.fileName }} ({{ props.fileType }})</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, Warning, Document } from '@element-plus/icons-vue';

// 导入 vue-office 组件
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import { Session } from '/@/utils/storage';

// 导入 vue-office 样式文件
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';

// Props 定义
interface Props {
  src: string | ArrayBuffer | Blob;
  fileType?: string;
  fileName?: string;
  height?: string;
  width?: string;
}

const props = withDefaults(defineProps<Props>(), {
  fileType: '',
  fileName: '',
  height: '100%',
  width: '100%'
});

// Emits 定义
const emit = defineEmits<{
  rendered: [];
  error: [error: string];
}>();

// 响应式数据
const loading = ref(false);
const error = ref('');
const documentSrc = ref<string | ArrayBuffer | Blob>('');

// 计算属性
const previewType = computed(() => {
  const fileType = props.fileType?.toLowerCase() || '';
  const fileName = props.fileName?.toLowerCase() || '';

  // 根据文件类型或文件名判断预览类型
  // Word 文档
  if (fileType.includes('word') || fileType.includes('docx') || fileType.includes('doc') ||
      fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
    return 'docx';
  }

  // Excel 文档
  if (fileType.includes('excel') || fileType.includes('xlsx') || fileType.includes('xls') ||
      fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    return 'excel';
  }

  // PowerPoint 文档 - vue-office 不直接支持 PPT，暂时标记为不支持
  if (fileType.includes('powerpoint') || fileType.includes('pptx') || fileType.includes('ppt') ||
      fileName.endsWith('.pptx') || fileName.endsWith('.ppt')) {
    return 'unsupported'; // PPT 暂不支持，需要下载查看
  }

  // PDF 文档 - 在 VueOfficePreview 中不处理 PDF，由 DocumentPreview 处理
  if (fileType.includes('pdf') || fileName.endsWith('.pdf')) {
    return 'unsupported';
  }

  return 'unsupported';
});

const previewStyle = computed(() => ({
  height: props.height,
  width: props.width
}));

// 方法
const loadDocument = async () => {
  if (!props.src) {
    error.value = '缺少文档源';
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    // 如果是 URL 字符串，需要获取文档内容
    if (typeof props.src === 'string') {
      if (props.src.startsWith('http') || props.src.startsWith('/')) {
        // 网络地址或相对路径，需要获取文档的二进制内容
        console.log('正在获取文档内容:', props.src);

        // 获取token用于认证
        const token = Session.get('token');
        const headers: Record<string, string> = {};
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // 构建完整URL
        const fullUrl = props.src.startsWith('http') ? props.src : `${window.location.origin}${props.src}`;
        console.log('请求文档URL:', fullUrl);

        const response = await fetch(fullUrl, {
          method: 'GET',
          headers
        });

        if (!response.ok) {
          throw new Error(`获取文档失败: ${response.status} ${response.statusText}`);
        }

        // 检查响应的 Content-Type
        const contentType = response.headers.get('content-type') || '';
        console.log('响应 Content-Type:', contentType);

        // 获取文档的二进制内容
        const arrayBuffer = await response.arrayBuffer();
        console.log('文档内容获取成功，大小:', arrayBuffer.byteLength);

        // 验证文档内容
        if (arrayBuffer.byteLength === 0) {
          throw new Error('获取的文档文件为空');
        }

        // 检查文件头，确保是有效的 Office 文档
        const uint8Array = new Uint8Array(arrayBuffer);
        const fileHeader = Array.from(uint8Array.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ');
        console.log('文件头 (前8字节):', fileHeader);

        // 检查是否是 ZIP 格式 (现代 Office 文档基于 ZIP) 或者是旧格式 Office 文档
        const isZip = uint8Array[0] === 0x50 && uint8Array[1] === 0x4B; // PK header
        const isOldOffice = uint8Array[0] === 0xD0 && uint8Array[1] === 0xCF; // OLE2 header
        console.log('文档格式检查 - ZIP:', isZip, 'OLE2:', isOldOffice);

        if (!isZip && !isOldOffice && arrayBuffer.byteLength > 0) {
          // 如果不是已知的 Office 格式，检查是否是错误响应
          const textContent = new TextDecoder().decode(arrayBuffer.slice(0, 500));
          console.log('文件内容预览 (前500字符):', textContent);

          // 检查是否是 HTML 错误页面
          if (textContent.includes('<html') || textContent.includes('<!DOCTYPE')) {
            throw new Error('服务器返回了 HTML 页面而不是文档文件，可能是认证失败或文件不存在');
          }

          // 检查是否是 JSON 错误响应
          if (textContent.trim().startsWith('{') || textContent.trim().startsWith('[')) {
            try {
              const jsonResponse = JSON.parse(textContent);
              throw new Error(`服务器返回错误: ${jsonResponse.message || jsonResponse.detail || '未知错误'}`);
            } catch (parseError) {
              // 如果不是有效的 JSON，继续原来的错误
            }
          }

          console.warn(`文档格式可能不标准，但仍尝试预览。文件大小: ${arrayBuffer.byteLength} 字节`);
        }

        documentSrc.value = arrayBuffer;
      } else {
        throw new Error('不支持的文档源格式');
      }
    } else {
      // ArrayBuffer 或 Blob，直接使用
      documentSrc.value = props.src;
    }
  } catch (err) {
    error.value = `加载文档失败: ${err instanceof Error ? err.message : '未知错误'}`;
    emit('error', error.value);
  } finally {
    loading.value = false;
  }
};

// 事件处理
const handleRendered = () => {
  console.log('Vue Office 文档渲染完成');
  emit('rendered');
};

const handleError = (err: any) => {
  const errorMsg = `文档渲染失败: ${err?.message || '未知错误'}`;
  error.value = errorMsg;
  console.error('Vue Office 渲染错误:', err);
  console.error('错误详情:', {
    message: err?.message,
    stack: err?.stack,
    name: err?.name,
    fileName: props.fileName,
    fileType: props.fileType,
    srcType: typeof props.src,
    srcLength: props.src instanceof ArrayBuffer ? props.src.byteLength : 'N/A'
  });
  ElMessage.error(errorMsg);
  emit('error', errorMsg);
};

// 监听 props 变化
watch(() => props.src, () => {
  if (props.src) {
    loadDocument();
  }
}, { immediate: true });

watch(() => props.fileType, () => {
  if (props.src) {
    loadDocument();
  }
});

// 生命周期
onMounted(() => {
  if (props.src) {
    loadDocument();
  }
});
</script>

<style scoped>
.vue-office-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-loading,
.preview-error,
.unsupported-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #909399;
  padding: 40px;
  text-align: center;
}

.office-preview-container {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.unsupported-preview h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.unsupported-preview p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.unsupported-preview .file-info {
  margin-top: 16px;
  font-size: 12px;
  color: #c0c4cc;
  font-family: monospace;
}
</style>
