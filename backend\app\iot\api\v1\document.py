#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档管理 API

提供文档的CRUD操作接口，基于Java token认证系统进行权限控制
严格遵循RAGFlow API规范和指南中的开发原则
"""
import uuid
import io
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, UploadFile, File, Form
from fastapi.responses import StreamingResponse

from backend.common.log import log as logger
from backend.app.iot.schema.document import (
    DocumentUpload,
    DocumentUpdate,
    DocumentQuery,
    DocumentDelete,
    DocumentParseControl,
    DocumentInfo,
    DocumentList,
    DocumentUploadResponse,
    FileUploadProgress
)
from backend.app.iot.service.document_service import document_service
from backend.app.iot.utils.file_upload import file_upload_handler
from backend.common.response.response_schema import ResponseModel, response_base
from backend.common.response.response_code import CustomResponse
from backend.common.security.jwt import DependsJwtAuth
from backend.common.security.java_permission import require_java_permission

router = APIRouter()


# 特定路由必须在通配符路由之前定义，避免路由冲突

@router.get(
    '/upload-progress/{upload_id}',
    summary='查询上传进度',
    response_model=ResponseModel,
    name='get_upload_progress'
)
async def get_upload_progress(
    upload_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    查询文件上传进度

    不需要特殊权限，只要有有效token即可
    """
    try:
        # 这里可以从缓存或数据库中获取进度信息
        # 目前返回一个示例响应
        progress_data = {
            "upload_id": upload_id,
            "status": "completed",
            "progress": 1.0,
            "message": "上传完成"
        }

        return response_base.success(
            res=CustomResponse(200, "获取上传进度成功"),
            data=progress_data
        )

    except Exception as e:
        logger.error(f"获取上传进度失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取上传进度失败: {str(e)}")
        )


@router.get(
    '/conversion-progress/{task_id}',
    summary='查询文档转换进度',
    response_model=ResponseModel,
    name='get_conversion_progress'
)
async def get_conversion_progress(
    task_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    查询文档转换进度

    不需要特殊权限，只要有有效token即可
    """
    try:
        result = await document_service.get_conversion_progress(task_id)

        return response_base.success(
            res=CustomResponse(200, "获取转换进度成功"),
            data=result
        )

    except Exception as e:
        logger.error(f"获取转换进度失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取转换进度失败: {str(e)}")
        )


@router.get(
    '/cache/stats',
    summary='获取缓存统计信息',
    response_model=ResponseModel,
    name='get_cache_stats'
)
@require_java_permission("knowledge:base:view")
async def get_cache_stats(
    request: Request,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取文档预览缓存统计信息

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.get_cache_stats()

        return response_base.success(
            res=CustomResponse(200, "获取缓存统计成功"),
            data=result
        )

    except Exception as e:
        logger.error(f"获取缓存统计失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取缓存统计失败: {str(e)}")
        )


# 通配符路由，必须放在最后

@router.delete(
    '/{kb_id}/{doc_id}/cache',
    summary='清除文档缓存',
    response_model=ResponseModel,
    name='clear_document_cache'
)
@require_java_permission("knowledge:base:update")
async def clear_document_cache(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    清除指定文档的缓存

    需要knowledge:base:update权限
    """
    try:
        result = await document_service.clear_document_cache(doc_id)

        return response_base.success(
            res=CustomResponse(200, "清除缓存成功"),
            data=result
        )

    except Exception as e:
        logger.error(f"清除文档缓存失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"清除文档缓存失败: {str(e)}")
        )


@router.post(
    '/{kb_id}/upload',
    summary='上传文档到知识库',
    response_model=ResponseModel,
    name='upload_document'
)
@require_java_permission("knowledge:base:create")
async def upload_document(
    request: Request,
    kb_id: str,
    file: UploadFile = File(..., description="上传的文件"),
    parser_id: str = Form("naive", description="解析器ID"),
    run_after_upload: bool = Form(True, description="上传后是否立即解析"),
    upload_id: Optional[str] = Form(None, description="上传ID，用于进度跟踪"),
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    上传文档到指定知识库

    需要knowledge:document:upload权限

    支持的文件类型：PDF、Word、Excel、TXT、Markdown等
    支持上传进度跟踪
    """
    try:
        # 生成上传ID（如果未提供）
        if not upload_id:
            upload_id = str(uuid.uuid4())

        # 构建上传参数
        upload_data = DocumentUpload(
            kb_id=kb_id,
            parser_id=parser_id,
            run_after_upload=run_after_upload
        )

        result = await document_service.upload_document(kb_id, file, upload_data, upload_id)

        # 添加上传ID到响应
        response_data = result.get("data", {})
        response_data["upload_id"] = upload_id

        return response_base.success(
            res=CustomResponse(200, "文档上传成功"),
            data=response_data
        )

    except HTTPException as e:
        logger.error(f"上传文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"上传文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"上传文档失败: {str(e)}")
        )


@router.get(
    '/{kb_id}/list',
    summary='获取文档列表',
    response_model=ResponseModel,
    name='list_documents'
)
@require_java_permission("knowledge:base:list")
async def list_documents(
    request: Request,
    kb_id: str,
    query: DocumentQuery = Depends(),
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取指定知识库的文档列表
    
    需要knowledge:document:list权限
    
    支持分页、搜索、排序等功能
    """
    try:
        # 设置知识库ID
        query.kb_id = kb_id
        
        result = await document_service.list_documents(query)
        
        return response_base.success(
            res=CustomResponse(200, "获取文档列表成功"),
            data=result.get("data", [])
        )
        
    except HTTPException as e:
        logger.error(f"获取文档列表失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档列表失败: {str(e)}")
        )


@router.get(
    '/{kb_id}/{doc_id}',
    summary='获取文档详情',
    response_model=ResponseModel,
    name='get_document'
)
@require_java_permission("knowledge:base:view")
async def get_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    获取指定文档的详细信息
    
    需要knowledge:document:view权限
    """
    try:
        result = await document_service.get_document(kb_id, doc_id)
        
        return response_base.success(
            res=CustomResponse(200, "获取文档详情成功"),
            data=result.get("data")
        )
        
    except HTTPException as e:
        logger.error(f"获取文档详情失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档详情失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档详情失败: {str(e)}")
        )


@router.put(
    '/{kb_id}/{doc_id}',
    summary='更新文档信息',
    response_model=ResponseModel,
    name='update_document'
)
@require_java_permission("knowledge:base:update")
async def update_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    update_data: DocumentUpdate,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    更新指定文档的信息
    
    需要knowledge:document:update权限
    """
    try:
        result = await document_service.update_document(kb_id, doc_id, update_data)
        
        return response_base.success(
            res=CustomResponse(200, "更新文档成功"),
            data=result.get("data")
        )
        
    except HTTPException as e:
        logger.error(f"更新文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"更新文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"更新文档失败: {str(e)}")
        )


@router.delete(
    '/{kb_id}/delete',
    summary='删除文档',
    response_model=ResponseModel,
    name='delete_documents'
)
@require_java_permission("knowledge:base:delete")
async def delete_documents(
    request: Request,
    kb_id: str,
    delete_data: DocumentDelete,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    删除指定的文档
    
    需要knowledge:document:delete权限
    
    支持批量删除
    """
    try:
        # 设置知识库ID
        delete_data.kb_id = kb_id
        
        result = await document_service.delete_documents(delete_data)
        
        return response_base.success(
            res=CustomResponse(200, "删除文档成功"),
            data=result.get("data")
        )
        
    except HTTPException as e:
        logger.error(f"删除文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"删除文档失败: {str(e)}")
        )


@router.post(
    '/{kb_id}/{doc_id}/parse',
    summary='开始文档解析',
    response_model=ResponseModel,
    name='start_document_parsing'
)
@require_java_permission("knowledge:base:update")
async def start_document_parsing(
    request: Request,
    kb_id: str,
    doc_id: str,
    parse_control: DocumentParseControl,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    开始解析指定文档
    
    需要knowledge:document:parse权限
    """
    try:
        # 设置知识库ID和文档ID
        parse_control.kb_id = kb_id
        parse_control.doc_id = doc_id
        
        result = await document_service.start_document_parsing(parse_control)
        
        return response_base.success(
            res=CustomResponse(200, "开始文档解析"),
            data=result.get("data")
        )
        
    except HTTPException as e:
        logger.error(f"开始文档解析失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"开始文档解析失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"开始文档解析失败: {str(e)}")
        )


@router.delete(
    '/{kb_id}/{doc_id}/parse',
    summary='停止文档解析',
    response_model=ResponseModel,
    name='stop_document_parsing'
)
@require_java_permission("knowledge:base:update")
async def stop_document_parsing(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    停止解析指定文档

    需要knowledge:document:parse权限
    """
    try:
        result = await document_service.stop_document_parsing(kb_id, doc_id)

        return response_base.success(
            res=CustomResponse(200, "停止文档解析"),
            data=result.get("data")
        )

    except HTTPException as e:
        logger.error(f"停止文档解析失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"停止文档解析失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"停止文档解析失败: {str(e)}")
        )


@router.post(
    '/{kb_id}/{doc_id}/convert-to-pdf',
    summary='将Office文档转换为PDF',
    response_model=ResponseModel,
    name='convert_document_to_pdf'
)
@require_java_permission("knowledge:base:view")
async def convert_document_to_pdf(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
) -> ResponseModel:
    """
    将Office文档转换为PDF格式用于预览

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.convert_document_to_pdf(kb_id, doc_id)

        return response_base.success(
            res=CustomResponse(200, "文档转换成功"),
            data=result
        )

    except HTTPException as e:
        logger.error(f"文档转换失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"文档转换失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"文档转换失败: {str(e)}")
        )


@router.get(
    '/{kb_id}/{doc_id}/download',
    summary='下载文档',
    name='download_document'
)
@require_java_permission("knowledge:base:view")
async def download_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
):
    """
    下载指定文档

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.download_document(kb_id, doc_id)

        # 返回文件流
        return StreamingResponse(
            result.get("content"),
            media_type=result.get("content_type", "application/octet-stream"),
            headers={
                "Content-Disposition": f"attachment; filename={result.get('filename', 'document')}"
            }
        )

    except HTTPException as e:
        logger.error(f"下载文档失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"下载文档失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"下载文档失败: {str(e)}")
        )


@router.get("/{kb_id}/{doc_id}/content", summary="获取文档内容用于预览")
@require_java_permission("knowledge:base:view")
async def get_document_content(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
):
    """
    获取文档内容用于预览（不带下载头信息）

    专门为vue-office等预览组件提供文档内容
    需要knowledge:base:view权限
    """
    try:
        result = await document_service.download_document(kb_id, doc_id)

        # 返回文件流，不带下载头信息
        return StreamingResponse(
            result.get("content"),
            media_type=result.get("content_type", "application/octet-stream"),
            headers={
                "Cache-Control": "no-cache",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET",
                "Access-Control-Allow-Headers": "Authorization, Content-Type"
            }
        )

    except HTTPException as e:
        logger.error(f"获取文档内容失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档内容失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档内容失败: {str(e)}")
        )


@router.get(
    '/{kb_id}/{doc_id}/converted.pdf',
    summary='获取转换后的PDF文档',
    name='get_converted_pdf'
)
@require_java_permission("knowledge:base:view")
async def get_converted_pdf(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
):
    """
    获取转换后的PDF文档

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.get_converted_pdf(kb_id, doc_id)

        # 返回PDF文件流
        return StreamingResponse(
            result.get("content"),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"inline; filename={result.get('filename', 'converted.pdf')}"
            }
        )

    except HTTPException as e:
        logger.error(f"获取转换PDF失败: {e.detail}")
        # 返回错误PDF而不是JSON响应
        error_pdf = document_service._generate_error_pdf(f"获取PDF失败: {e.detail}")
        return StreamingResponse(
            io.BytesIO(error_pdf),
            media_type="application/pdf",
            headers={
                "Content-Disposition": "inline; filename=error.pdf"
            }
        )
    except Exception as e:
        logger.error(f"获取转换PDF失败: {str(e)}")
        # 返回错误PDF而不是JSON响应
        error_pdf = document_service._generate_error_pdf(f"获取PDF失败: {str(e)}")
        return StreamingResponse(
            io.BytesIO(error_pdf),
            media_type="application/pdf",
            headers={
                "Content-Disposition": "inline; filename=error.pdf"
            }
        )



@router.get(
    '/{kb_id}/{doc_id}/preview',
    summary='获取文档预览',
    name='preview_document'
)
@require_java_permission("knowledge:base:view")
async def preview_document(
    request: Request,
    kb_id: str,
    doc_id: str,
    token: str = DependsJwtAuth
):
    """
    获取文档预览内容

    需要knowledge:base:view权限
    """
    try:
        result = await document_service.get_document_preview(kb_id, doc_id)

        # 统一返回JSON格式响应
        return response_base.success(
            res=CustomResponse(200, "获取预览成功"),
            data=result
        )

    except HTTPException as e:
        logger.error(f"获取文档预览失败: {e.detail}")
        return response_base.fail(
            res=CustomResponse(e.status_code, e.detail)
        )
    except Exception as e:
        logger.error(f"获取文档预览失败: {str(e)}")
        return response_base.fail(
            res=CustomResponse(500, f"获取文档预览失败: {str(e)}")
        )



